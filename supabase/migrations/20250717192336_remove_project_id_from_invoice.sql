drop policy "Users can create invoices for projects they can edit" on "public"."invoice";

drop policy "Users can delete invoices for projects they can edit" on "public"."invoice";

drop policy "Users can update invoices for projects they can edit" on "public"."invoice";

drop policy "Users can view invoices for accessible projects" on "public"."invoice";

drop policy "Users can view invoice audit for accessible projects" on "public"."invoice_audit";

alter table "public"."invoice" drop constraint "invoice_project_id_fkey";

drop index if exists "public"."invoice_audit_project_id_idx";

drop index if exists "public"."invoice_project_id_idx";

alter table "public"."invoice" drop column "project_id";

alter table "public"."invoice_audit" drop column "project_id";

set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.audit_invoice_changes()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        v_user_id := '********-0000-0000-0000-************'::UUID;
    END IF;

    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.invoice_audit (
            operation_type, changed_by, changed_at, old_values,
            invoice_id, purchase_order_id, description, invoice_date,
            account, amount, period, post_date, notes,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'DELETE', v_user_id, NOW(), to_jsonb(OLD),
            OLD.invoice_id, OLD.purchase_order_id, OLD.description, OLD.invoice_date,
            OLD.account, OLD.amount, OLD.period, OLD.post_date, OLD.notes,
            OLD.created_by_user_id, OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.invoice_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            invoice_id, purchase_order_id, description, invoice_date,
            account, amount, period, post_date, notes,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'UPDATE', v_user_id, NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.invoice_id, NEW.purchase_order_id, NEW.description, NEW.invoice_date,
            NEW.account, NEW.amount, NEW.period, NEW.post_date, NEW.notes,
            NEW.created_by_user_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.invoice_audit (
            operation_type, changed_by, changed_at, new_values,
            invoice_id, purchase_order_id, description, invoice_date,
            account, amount, period, post_date, notes,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'INSERT', v_user_id, NOW(), to_jsonb(NEW),
            NEW.invoice_id, NEW.purchase_order_id, NEW.description, NEW.invoice_date,
            NEW.account, NEW.amount, NEW.period, NEW.post_date, NEW.notes,
            NEW.created_by_user_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_accessible_invoices(project_id_param uuid)
 RETURNS TABLE(invoice_id uuid, purchase_order_id uuid, po_number text, description text, invoice_date date, vendor_name text, account text, amount numeric, period text, post_date date, notes text, created_at timestamp with time zone, created_by_name text)
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
DECLARE
	v_user_id UUID;
BEGIN
	-- Get the current user ID
	v_user_id := auth.uid();

	-- Check if user has access to the project
	IF NOT public.current_user_has_entity_access('project'::public.entity_type, project_id_param) THEN
		RAISE EXCEPTION 'Access denied to project';
	END IF;

	-- Return invoices for the project with vendor, purchase order, and creator information
	RETURN QUERY
	SELECT
		i.invoice_id,
		i.purchase_order_id,
		po.po_number,
		i.description,
		i.invoice_date,
		v.name AS vendor_name,
		i.account,
		i.amount,
		i.period,
		i.post_date,
		i.notes,
		i.created_at,
		p.full_name AS created_by_name
	FROM public.invoice i
	INNER JOIN public.purchase_order po ON i.purchase_order_id = po.purchase_order_id
	LEFT JOIN public.vendor v ON po.vendor_id = v.vendor_id
	LEFT JOIN public.profile p ON i.created_by_user_id = p.user_id
	WHERE po.project_id = project_id_param
	ORDER BY i.invoice_date DESC, po.po_number;
END;
$function$
;

create policy "Users can create invoices for projects they can edit"
on "public"."invoice"
as permissive
for insert
to authenticated
with check ((EXISTS ( SELECT 1
   FROM purchase_order po
  WHERE ((po.purchase_order_id = invoice.purchase_order_id) AND current_user_has_entity_role('project'::entity_type, po.project_id, 'editor'::membership_role)))));


create policy "Users can delete invoices for projects they can edit"
on "public"."invoice"
as permissive
for delete
to authenticated
using ((EXISTS ( SELECT 1
   FROM purchase_order po
  WHERE ((po.purchase_order_id = invoice.purchase_order_id) AND current_user_has_entity_role('project'::entity_type, po.project_id, 'editor'::membership_role)))));


create policy "Users can update invoices for projects they can edit"
on "public"."invoice"
as permissive
for update
to authenticated
using ((EXISTS ( SELECT 1
   FROM purchase_order po
  WHERE ((po.purchase_order_id = invoice.purchase_order_id) AND current_user_has_entity_role('project'::entity_type, po.project_id, 'editor'::membership_role)))))
with check ((EXISTS ( SELECT 1
   FROM purchase_order po
  WHERE ((po.purchase_order_id = invoice.purchase_order_id) AND current_user_has_entity_role('project'::entity_type, po.project_id, 'editor'::membership_role)))));


create policy "Users can view invoices for accessible projects"
on "public"."invoice"
as permissive
for select
to authenticated
using ((EXISTS ( SELECT 1
   FROM purchase_order po
  WHERE ((po.purchase_order_id = invoice.purchase_order_id) AND current_user_has_entity_access('project'::entity_type, po.project_id)))));


create policy "Users can view invoice audit for accessible projects"
on "public"."invoice_audit"
as permissive
for select
to authenticated
using ((EXISTS ( SELECT 1
   FROM purchase_order po
  WHERE ((po.purchase_order_id = invoice_audit.purchase_order_id) AND current_user_has_entity_access('project'::entity_type, po.project_id)))));




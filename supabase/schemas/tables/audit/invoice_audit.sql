-- Invoice Audit Table Schema
-- Audit log of all changes to invoice entries
-- Invoice Audit table
CREATE TABLE IF NOT EXISTS "public"."invoice_audit" (
	"audit_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"operation_type" "text" NOT NULL CHECK (
		"operation_type" IN ('INSERT', 'UPDATE', 'DELETE')
	),
	"changed_by" "uuid" NOT NULL,
	"changed_at" timestamp with time zone DEFAULT "now" () NOT NULL,
	"old_values" "jsonb",
	"new_values" "jsonb",
	-- Original table columns
	"invoice_id" "uuid",
	"purchase_order_id" "uuid",
	"description" "text",
	"invoice_date" "date",
	"account" "text",
	"amount" numeric(15, 2),
	"period" "text",
	"post_date" "date",
	"notes" "text",
	"project_id" "uuid",
	"created_by_user_id" "uuid",
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);

ALTER TABLE "public"."invoice_audit" OWNER TO "postgres";

COMMENT ON TABLE "public"."invoice_audit" IS 'Audit log of all changes to invoice entries';

-- Primary key constraint for audit table
ALTER TABLE ONLY "public"."invoice_audit"
ADD CONSTRAINT "invoice_audit_pkey" PRIMARY KEY ("audit_id");

-- Foreign key constraint for audit table
ALTER TABLE ONLY "public"."invoice_audit"
ADD CONSTRAINT "invoice_audit_changed_by_fkey" FOREIGN KEY ("changed_by") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

-- Indexes for audit table performance
CREATE INDEX "invoice_audit_invoice_id_idx" ON "public"."invoice_audit" USING "btree" ("invoice_id");

CREATE INDEX "invoice_audit_changed_by_idx" ON "public"."invoice_audit" USING "btree" ("changed_by");

CREATE INDEX "invoice_audit_changed_at_idx" ON "public"."invoice_audit" USING "btree" ("changed_at");

CREATE INDEX "invoice_audit_operation_type_idx" ON "public"."invoice_audit" USING "btree" ("operation_type");

CREATE INDEX "invoice_audit_project_id_idx" ON "public"."invoice_audit" USING "btree" ("project_id");

-- Enable Row Level Security for audit table
ALTER TABLE "public"."invoice_audit" ENABLE ROW LEVEL SECURITY;

-- Row Level Security Policies for Audit Table
CREATE POLICY "System can insert invoice audit records" ON "public"."invoice_audit" FOR INSERT TO "service_role"
WITH
	CHECK (true);

CREATE POLICY "Users can view invoice audit for accessible projects" ON "public"."invoice_audit" FOR
SELECT
	TO "authenticated" USING (
		"public"."current_user_has_entity_access" ('project'::"public"."entity_type", "project_id")
	);
